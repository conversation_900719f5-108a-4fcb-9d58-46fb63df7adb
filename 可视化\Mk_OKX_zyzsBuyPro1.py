#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
实盘交易多重确认阶段性止盈止损策略 (精简实盘单点判断模式，无日志功能，无“天数”字段)
作者: AI Assistant
版本: 3.5 (修正字段名称为“当前回撤”)
适用场景: 模拟实盘中逐个价格点（K线高低价）传入策略进行判断，并返回实时决策及详细状态。
"""

import numpy as np
import pandas as pd
from typing import Dict, Optional, List
from datetime import datetime
import json
import warnings
warnings.filterwarnings('ignore', category=UserWarning)

# --- 猴子补丁开始 ---
if not hasattr(np, 'NaN'):
    np.NaN = np.nan
# --- 猴子补丁结束 ---

class LiveTradingStrategy:
    """
    实盘交易多重确认阶段性止盈止损策略类 (仅用于多头)
    
    核心思想：
    - 阶段0: 风险期，宽松止损等待确认
    - 阶段1: 确认期，收紧止损
    - 阶段2: 安全期，止损移至盈利区
    - 阶段3: 跟踪期，动态跟踪止损
    
    特点：
    - 支持逐个价格点处理 (接收K线高低价)
    - 维持内部状态
    - **无内部日志功能，仅输出策略判断结果。**
    - 明确只支持多头（做多）策略。
    """
    
    def __init__(self, 
                 stage1_threshold: float = 0.005,   # 进入阶段1的盈利阈值 (0.5%)
                 stage2_threshold: float = 0.015,   # 进入阶段2的盈利阈值 (1.5%)
                 stage3_threshold: float = 0.025,   # 进入阶段3的盈利阈值 (2.5%)
                 initial_stop_loss_ratio: float = 0.035, # 初始止损比例 (1.5%)
                 stage1_stop_loss_ratio: float = 0.005,  # 阶段1止损比例 (0.5%)
                 stage2_stop_profit_ratio: float = 0.005, # 阶段2止损设在盈利区 (0.5%)
                 trailing_ratio: float = 0.25,       # 跟踪止损回撤比例 (30%)
                 min_profit_lock_ratio: float = 0.002):   # 最小利润锁定 (0.2%)
        """
        初始化策略参数
        """
        self.stage1_threshold = stage1_threshold
        self.stage2_threshold = stage2_threshold
        self.stage3_threshold = stage3_threshold
        self.initial_stop_loss_ratio = initial_stop_loss_ratio
        self.stage1_stop_loss_ratio = stage1_stop_loss_ratio
        self.stage2_stop_profit_ratio = stage2_stop_profit_ratio
        self.trailing_ratio = trailing_ratio
        self.min_profit_lock_ratio = min_profit_lock_ratio
        
        self.reset()
    
    def reset(self):
        """重置策略状态"""
        self.position = False
        self.entry_price = 0.0
        self.current_stage = 0
        self.stop_loss = 0.0
        self.high_water_mark = 0.0 
        self.entry_time = None
        self.tick_count = 0
        self.last_processed_price = 0.0 
        
        self.price_history = []
        self.stop_loss_history = []
        self.stage_history = []
        self.profit_history = []
        
        self.closed_trade_final_result = None # 用于存储最终平仓时的结果
        
    def enter_position(self, price: float, signal_info: Optional[Dict] = None) -> Dict:
        """
        开仓操作 (多头)
        
        Args:
            price: 开仓价格
            signal_info: 信号信息 (可选)
            
        Returns:
            Dict: 开仓结果信息
        """
        if self.position:
            return self._format_realtime_output(
                action='ALREADY_IN_POSITION', 
                price=price, 
                message='已有持仓，无法重复开仓',
                profit_pct=self._calculate_profit_pct(price),
                stop_loss=self.stop_loss,
                current_stage=self.current_stage,
                high_water_mark=self.high_water_mark
            )
        
        self.reset() # 每次开仓前重置所有状态，确保干净开始
        self.position = True
        self.entry_price = price
        self.current_stage = 0
        self.stop_loss = price * (1 - self.initial_stop_loss_ratio) 
        self.high_water_mark = price 
        self.entry_time = datetime.now()
        self.last_processed_price = price
        
        self.price_history.append(price)
        self.stop_loss_history.append(self.stop_loss)
        self.stage_history.append(0)
        self.profit_history.append(0.0)
        
        return self._format_realtime_output(
            action='ENTER', 
            price=price, 
            message='开仓成功', 
            signal_info=signal_info,
            profit_pct=0.0,
            stop_loss=self.stop_loss,
            current_stage=self.current_stage,
            high_water_mark=self.high_water_mark
        )
    
    def process_tick(self, price_high: float, price_low: float, timestamp: Optional[datetime] = None) -> Dict:
        """
        处理单个价格tick (实盘核心函数)
        
        Args:
            price_high: 当前K线周期内的最高价
            price_low: 当前K线周期内的最低价
            timestamp: 当前K线周期的时间戳 (可选)
            
        Returns:
            Dict: 实时判断结果，格式化为用户所需
        """
        self.tick_count += 1
        current_time = timestamp or datetime.now()
        
        if not self.position:
            return self._format_realtime_output(
                action='NO_POSITION', 
                price=price_high, 
                message='当前无持仓，等待开仓信号',
                k_line_high=price_high,
                k_line_low=price_low,
                profit_pct=0.0,
                stop_loss=0.0,
                current_stage=0,
                high_water_mark=0.0
            )
        
        # --- 核心策略逻辑 ---
        original_stop_loss = self.stop_loss
        original_high_water_mark = self.high_water_mark
        
        # 1. 首先用最低价判断是否止损 (严格回测)
        temp_profit_at_low = (price_low - self.entry_price) / self.entry_price
        self.high_water_mark = max(self.high_water_mark, price_low) # 基于低价更新最高水位以判断止损
        self._update_stage(temp_profit_at_low)
        self._update_stop_loss(temp_profit_at_low)

        # 如果最低价已经触发止损，则直接平仓
        if price_low <= self.stop_loss:
            exit_result = self._exit_position(price_low, 'STOP_LOSS', current_time)
            self.closed_trade_final_result = exit_result 
            return self._format_realtime_output(
                action='EXIT', 
                price=price_low, 
                message=exit_result['message'],
                final_profit=exit_result['final_profit_pct'],
                max_profit=exit_result['max_profit_pct'],
                final_stage=exit_result['final_stage'],
                k_line_high=price_high, 
                k_line_low=price_low,
                profit_pct=exit_result['final_profit_pct'],
                stop_loss=self.stop_loss,
                current_stage=self.current_stage,
                high_water_mark=self.high_water_mark
            )

        # 2. 如果未止损，再用最高价更新高水位并继续处理 (可能阶段升级，止损上移)
        self.high_water_mark = max(original_high_water_mark, price_high) # 确保高水位能被最高价正确更新
        current_profit_at_high = (price_high - self.entry_price) / self.entry_price
        self._update_stage(current_profit_at_high)
        self._update_stop_loss(current_profit_at_high)
        
        self.price_history.append(price_low) 
        self.stop_loss_history.append(self.stop_loss)
        self.stage_history.append(self.current_stage)
        self.profit_history.append(current_profit_at_high * 100) 
        self.last_processed_price = price_high 

        return self._format_realtime_output(
            action='HOLD', 
            price=price_high, 
            message='继续持有',
            profit_pct=current_profit_at_high * 100,
            stop_loss=self.stop_loss,
            current_stage=self.current_stage,
            high_water_mark=self.high_water_mark,
            k_line_high=price_high,
            k_line_low=price_low,
            current_day_high_vs_entry_ratio_growth=None # 传入None，让_format_realtime_output自行计算
        )
    
    def _update_stage(self, profit: float):
        """更新交易阶段 (仅多头)"""
        if self.current_stage == 0 and profit >= self.stage1_threshold:
            self.current_stage = 1
        elif self.current_stage == 1 and profit >= self.stage2_threshold:
            self.current_stage = 2
        elif self.current_stage == 2 and profit >= self.stage3_threshold:
            self.current_stage = 3
    
    def _update_stop_loss(self, profit: float):
        """更新止损价格 (仅多头)"""
        if self.current_stage == 0:
            pass 
        elif self.current_stage == 1:
            new_stop = self.entry_price * (1 - self.stage1_stop_loss_ratio)
            self.stop_loss = max(self.stop_loss, new_stop) 
        elif self.current_stage == 2:
            new_stop = self.entry_price * (1 + self.stage2_stop_profit_ratio)
            self.stop_loss = max(self.stop_loss, new_stop) 
        elif self.current_stage == 3:
            trailing_distance = (self.high_water_mark - self.entry_price) * self.trailing_ratio
            new_stop = self.high_water_mark - trailing_distance
            min_profit_stop = self.entry_price * (1 + self.min_profit_lock_ratio)
            self.stop_loss = max(self.stop_loss, new_stop, min_profit_stop) 
    
    def manual_exit(self, price: float, reason: str = 'MANUAL', timestamp: Optional[datetime] = None) -> Dict:
        """
        手动平仓
        """
        if not self.position:
            return self._format_realtime_output(
                action='NO_POSITION', 
                price=price, 
                message='当前无持仓，无法手动平仓',
                profit_pct=0.0,
                stop_loss=0.0,
                current_stage=0,
                high_water_mark=0.0
            )
        
        exit_result = self._exit_position(price, reason, timestamp)
        self.closed_trade_final_result = exit_result 
        return self._format_realtime_output(
            action='EXIT', 
            price=price, 
            message=exit_result['message'],
            final_profit=exit_result['final_profit_pct'],
            max_profit=exit_result['max_profit_pct'],
            final_stage=exit_result['final_stage'],
            profit_pct=exit_result['final_profit_pct'], 
            stop_loss=self.stop_loss,
            current_stage=self.current_stage,
            high_water_mark=self.high_water_mark
        )
    
    def _exit_position(self, price: float, reason: str, timestamp: Optional[datetime] = None) -> Dict:
        """内部平仓处理"""
        final_profit = (price - self.entry_price) / self.entry_price
        max_profit_achieved = (self.high_water_mark - self.entry_price) / self.entry_price 
        
        exit_time = timestamp or datetime.now()
        holding_duration = exit_time - self.entry_time if self.entry_time else None
        
        self.position = False
        
        return {
            'success': True,
            'action': 'EXIT',
            'reason': reason,
            'price': price,
            'entry_price': self.entry_price,
            'final_profit_pct': final_profit * 100,
            'max_profit_pct': max_profit_achieved * 100,
            'final_stage': self.current_stage,
            'holding_duration': str(holding_duration),
            'message': f"平仓：原因={reason}, 收益={final_profit*100:.2f}%"
        }
    
    def get_current_status(self) -> Dict:
        """获取当前状态信息 (仅多头)"""
        if not self.position:
            return {'has_position': False, 'message': '当前无持仓'}
        
        current_price_for_status = self.price_history[-1] if self.price_history else self.entry_price
        current_profit = (current_price_for_status - self.entry_price) / self.entry_price * 100
        
        distance_to_stop = ((current_price_for_status - self.stop_loss) / current_price_for_status * 100) if current_price_for_status > 0 else 0

        return {
            'has_position': True,
            'entry_price': self.entry_price,
            'current_price': current_price_for_status,
            'profit_pct': current_profit,
            'stop_loss': self.stop_loss,
            'current_stage': self.current_stage,
            'high_water_mark': self.high_water_mark,
            'tick_count': self.tick_count,
            'entry_time': self.entry_time.isoformat() if self.entry_time else None,
            'distance_to_stop_pct': distance_to_stop
        }

    def _calculate_profit_pct(self, price: float) -> float:
        """计算相对于开仓价的盈利百分比"""
        if self.entry_price == 0:
            return 0.0
        return ((price - self.entry_price) / self.entry_price) * 100

    def _format_realtime_output(self, action: str, price: float, message: str, 
                                 profit_pct: Optional[float] = None, 
                                 stop_loss: Optional[float] = None, 
                                 current_stage: Optional[int] = None,
                                 final_profit: Optional[float] = None,
                                 max_profit: Optional[float] = None,
                                 final_stage: Optional[int] = None,
                                 k_line_high: Optional[float] = None, 
                                 k_line_low: Optional[float] = None,  
                                 current_day_high_vs_entry_ratio_growth: Optional[float] = None, # 此参数现在被内部计算覆盖
                                 signal_info: Optional[Dict] = None, 
                                 high_water_mark: Optional[float] = None 
                                 ) -> Dict:
        """
        格式化实时输出结果为用户所需的形式，并翻译键名。
        """
        current_status_from_strategy = self.get_current_status() if self.position else {
            'entry_price': self.entry_price, 'high_water_mark': self.high_water_mark
        }
        
        realtime_profit_val = profit_pct if profit_pct is not None else current_status_from_strategy.get('profit_pct', 0.0)
        
        max_profit_val = max_profit 
        if max_profit_val is None: 
            effective_hwm = high_water_mark if high_water_mark is not None else self.high_water_mark
            if self.entry_price > 0:
                max_profit_val = ((effective_hwm - self.entry_price) / self.entry_price) * 100
            else:
                max_profit_val = 0.0

        # --- 修正：计算 "当前回撤" ---
        current_drawdown_value = 0.0
        # 只有在有持仓且有最低价、开仓价不为0时才计算
        if self.position and self.entry_price > 0 and k_line_low is not None and k_line_low > 0:
            current_drawdown_value = (self.entry_price / k_line_low) - 1
        
        output_data = {
            "参考价格": price, 
            "K线最高价": k_line_high if k_line_high is not None else np.NaN, 
            "K线最低价": k_line_low if k_line_low is not None else np.NaN,   
            "策略信号": {
                "动作": action,
                "信息": message
            },
            "当前策略状态": {
                "开仓价格": self.entry_price,
                "阶段": current_stage if current_stage is not None else current_status_from_strategy.get('current_stage', self.current_stage),
                "止损位": stop_loss if stop_loss is not None else current_status_from_strategy.get('stop_loss', self.stop_loss),
                "实时盈利": realtime_profit_val,
                "最高盈利": max_profit_val, 
                "当前回撤": current_drawdown_value * 100 # 新名称，新计算
            }
        }
        
        if action == 'EXIT':
            output_data["最终收益"] = final_profit
            output_data["最大浮盈"] = max_profit
            output_data["最终阶段"] = final_stage
            
            output_data["当前策略状态"]["实时盈利"] = final_profit
            output_data["当前策略状态"]["最高盈利"] = max_profit 

        output_data["当前价格信息"] = (
            f"当前价格: {price:.5f} (相对于开仓价上涨: {realtime_profit_val:.2f}%)"
        )
        output_data["止损价格"] = self.stop_loss 
        
        return output_data


# --- 辅助函数：将字典键翻译为中文 (保持不变) ---
def translate_keys_to_chinese(data: Dict) -> Dict:
    """递归地将字典中的英文键翻译为中文。"""
    translation_map = {
        "参考价格": "参考价格", "K线最高价": "K线最高价", 
        "K线最低价": "K线最低价", "策略信号": "策略信号", "动作": "动作", 
        "信息": "信息", "当前策略状态": "当前策略状态", "开仓价格": "开仓价格", 
        "阶段": "阶段", "止损位": "止损位", "实时盈利": "实时盈利", 
        "最高盈利": "最高盈利", "当前回撤": "当前回撤", # 修正翻译映射
        "最终收益": "最终收益", "最大浮盈": "最大浮盈", "最终阶段": "最终阶段",
        "当前价格信息": "当前价格信息", "止损价格": "止损价格",
        "action": "动作", "message": "信息", "profit_pct": "盈利百分比", 
        "stop_loss": "止损价格", "current_stage": "当前阶段", "high_water_mark": "最高水位",
        "reason": "原因", "price": "价格"
    }
    if isinstance(data, dict):
        new_dict = {}
        for k, v in data.items():
            new_key = translation_map.get(k, k) 
            new_dict[new_key] = translate_keys_to_chinese(v) 
        return new_dict
    elif isinstance(data, list):
        return [translate_keys_to_chinese(elem) for elem in data] 
    else:
        return data


# --- 模拟实盘主函数 (演示如何调用 LiveTradingStrategy) ---
if __name__ == "__main__":
    print("=== 模拟实盘交易策略单点判断模式 ===")

    from MkKu import load_json 
    import json  # Missing import
    
    buysellHistory = load_json('buysellHistory.json')
    highprice = buysellHistory[1][0]
    lowprice = buysellHistory[2][0]
    
    # 1. 实例化策略 (使用默认参数)
    strategy = LiveTradingStrategy() 
    current_day_counter = 0  # 模拟天数/tick计数
    # print(highprice, lowprice)

    # 模拟K线高低价数据流 (来自您的 buysellHistory 数据结构)
    # 这些是作为 K线高价 (current_price) 和 K线低价 (jizhiPrice) 传入的
    
    # --- 模拟开仓 ---
    print("\n--- 模拟开仓 ---")
    # 通常以K线某个价格作为入场价格，这里取第一个数据点的最高价
    entry_price_sim = highprice[0]
    entry_result = strategy.enter_position(entry_price_sim) 
    print("开仓返回数据:\n" + json.dumps(entry_result, indent=4, ensure_ascii=False))
    
    if entry_result['策略信号']['动作'] != 'ENTER':
        print("开仓失败，退出模拟。")
        exit()

    # --- 模拟逐日价格更新与判断 ---
    print("\n--- 模拟逐日价格更新与判断 ---")
    for i, price_high in enumerate(highprice[1:], 1):  # Fixed enumerate usage, start from index 1
        current_day_counter += 1  # 模拟经过的天数，从1开始
        price_low = lowprice[i]  # Get corresponding low price
        
        print(f"\n--- 处理第 {i} 天数据 (Tick {current_day_counter}) ---")
        print(f"传入 K线最高价 (current_price): {price_high:.5f}, K线最低价 (jizhiPrice): {price_low:.5f}")

        # 传入K线最高价和最低价给策略
        realtime_output = strategy.process_tick(price_high, price_low)
        
        # 打印策略的实时判断结果
        print("策略判断结果:\n" + json.dumps(realtime_output, indent=4, ensure_ascii=False))

        # 根据策略的动作进行后续操作 (在实盘中会发送订单)
        if realtime_output['策略信号']['动作'] == 'EXIT':
            print(f"!!! 策略发出平仓信号，原因: {realtime_output['策略信号']['信息']} !!!")
            # 此时您的实盘系统会发送平仓订单
            break  # 交易结束，退出模拟循环

    # --- 交易结束后的状态 ---
    print("\n--- 模拟结束 ---")
    if strategy.position:
        print(f"模拟价格序列结束，但仍有持仓。进行手动平仓。")
        # 手动平仓使用最后一个价格点 (这里用K线最低价作为平仓参考)
        final_price_for_exit = lowprice[-1]  # Fixed: removed ['low'] indexing
        final_output = strategy.manual_exit(final_price_for_exit, 'END_OF_SIMULATION')
        print("最终手动平仓结果:\n" + json.dumps(final_output, indent=4, ensure_ascii=False))
    else:
        # 如果已经因止损平仓，从策略中获取最终结果
        if strategy.closed_trade_final_result:
            print("\n本次交易已止损平仓，最终结果:")
            print(json.dumps(strategy.closed_trade_final_result, indent=4, ensure_ascii=False))
        else:
            print("\n本次交易已结束，无最终平仓信息（可能开仓失败或数据不足）。")

    print("\n=== 模拟完成 ===")