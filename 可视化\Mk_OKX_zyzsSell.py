
import matplotlib.pyplot as plt
import numpy as np
from typing import Optional
import warnings
warnings.filterwarnings('ignore', category=UserWarning)
from MkKu import load_json

def short_dynamic_stop_strategy(prices, jixianprice,entry_price=None, show_details=True):
    """
    空头多重确认阶段性止损策略
    
    Args:
        prices: 价格序列列表
        entry_price: 开仓价格，默认使用第一个价格
        show_details: 是否显示详细过程
    
    Returns:
        dict: 策略执行结果
    """
    if entry_price is None:
        entry_price = prices[0]
    
    # === 策略参数 (针对空头优化) ===
    initial_stop_loss = 0.02    # 初始止损+2% (抗住反弹)
    stage1_threshold = 0.008    # 0.8%盈利进入阶段1
    stage2_threshold = 0.018    # 1.8%盈利进入阶段2
    stage3_threshold = 0.025    # 2.5%盈利进入阶段3
    stage1_stop_loss = 0.012    # 阶段1止损+1.2%
    stage2_stop_profit = 0.005  # 阶段2止损设在-0.5%(盈利区)
    trailing_ratio = 0.35       # 跟踪止损35%回撤
    
    # === 交易状态初始化 ===
    position = True
    stage = 0
    stop_loss = entry_price * (1 + initial_stop_loss)  # 空头止损在上方
    lowest_price = entry_price  # 记录最低价(空头的高水位)
    maxkuisun=0
    # if show_details:
    #     print("=== 空头多重确认阶段性策略 ===")
    #     print(f"开仓价格: {entry_price:.5f}")
    #     print(f"初始止损: {stop_loss:.5f} (+{initial_stop_loss*100:.1f}%)")
    #             # 正确的计算
    #     print(f"理论收益: {((entry_price - prices[-1]) / entry_price * 100):.2f}%")
    #     print("-" * 50)
    
    # === 逐个处理价格 ===
    for i, current_price in enumerate(prices[1:], 1):
        if not position:
            break
        
        # 更新最低价(空头最有利位置)
        lowest_price = min(lowest_price, current_price)
        
        # 计算当前盈利(空头: 开仓价 - 当前价)
        current_profit = (entry_price - current_price) / entry_price
        
        # === 阶段升级逻辑 ===
        old_stage = stage
        
        if stage == 0 and current_profit >= stage1_threshold:
            stage = 1
            # 收紧止损到+1.2%
            new_stop = entry_price * (1 + stage1_stop_loss)
            stop_loss = min(stop_loss, new_stop)
            
        elif stage == 1 and current_profit >= stage2_threshold:
            stage = 2
            # 止损移至盈利区-0.5%
            new_stop = entry_price * (1 - stage2_stop_profit)
            stop_loss = min(stop_loss, new_stop)
            
        elif stage == 2 and current_profit >= stage3_threshold:
            stage = 3
        
        # === 阶段3跟踪止损 ===
        if stage == 3:
            profit_range = entry_price - lowest_price
            trailing_distance = profit_range * trailing_ratio
            new_stop = lowest_price + trailing_distance
            stop_loss = min(stop_loss, new_stop)
        
        # 显示阶段升级
        if show_details and old_stage != stage:
            print(f"第{i:2d}天: 阶段{old_stage}→{stage} | 价格:{current_price:.5f} | 盈利:{current_profit*100:.2f}% | 止损:{stop_loss:.5f}")
        
        # === 检查止损 (空头: 价格上涨触发) ===
        if current_price >= stop_loss:
            final_profit = (entry_price - current_price) / entry_price * 100
            max_profit = (entry_price - lowest_price) / entry_price * 100
            
            if show_details:
                print(f"第{i:2d}天: 🛑 触发止损!")
                print(f"当前价格: {current_price:.5f} (+{((current_price-entry_price)/entry_price*100):.2f}%)")
                print(f"止损价格: {stop_loss:.5f}")
                print(f"最终收益: {final_profit:.2f}%")
                print(f"最大浮盈: {max_profit:.2f}%")
                print(f"最终阶段: {stage}")
            
            return {
                'success': False,
                'exit_day': i,
                'exit_price': current_price,
                'final_profit': final_profit,
                'max_profit': max_profit,
                'final_stage': stage,
                'reason': 'STOP_LOSS'
            }
        
        #记录理论最大亏损值：
        if maxkuisun>(jixianprice[0]-jixianprice[i])/jixianprice[0]:
            maxkuisun=(jixianprice[0]-jixianprice[i])/jixianprice[0]
            print(f"第{i:2d}天,理论最大亏损更新{maxkuisun}")
        # if show_details and i % 15 == 0:
        #     print(f"第{i:2d}天: 价格:{current_price:.5f} | 阶段:{stage} | 止损:{stop_loss:.5f} 实际最高价{jixianprice[i]} |实际最大亏损比例{(jixianprice[0]-jixianprice[i])/jixianprice[0]}| 浮盈:{current_profit*100:.2f}%")
    
    # === 成功持有到最后 ===
    final_price = prices[-1]
    final_profit = (entry_price - final_price) / entry_price * 100
    max_profit = (entry_price - lowest_price) / entry_price * 100
    
    # if show_details:
    #     print(f"🎉 空头策略成功! 完整捕获趋势!")
    #     print(f"最终价格: {final_price:.5f}")
    #     print(f"最终收益: {final_profit:.2f}%")
    #     print(f"最大浮盈: {max_profit:.2f}%")
    #     print(f"最终阶段: {stage}")
    #     print(f"持有天数: {len(prices)-1}")
    
    return {
        'success': True,
        'exit_day': len(prices) - 1,
        'exit_price': final_price,
        'final_profit': final_profit,
        'max_profit': max_profit,
        'final_stage': stage,
        'reason': 'MANUAL_EXIT'
    }

def short_dynamic_stop_strategy_with_chart(prices, entry_price=None, show_details=True, show_chart=True, save_path=None):
    """
    带图表展示的空头策略执行函数
    
    Args:
        prices: 价格序列列表
        entry_price: 开仓价格，默认使用第一个价格
        show_details: 是否显示详细过程
        show_chart: 是否显示图表
        save_path: 图表保存路径，None则显示图表
    
    Returns:
        dict: 策略执行结果
    """
    if entry_price is None:
        entry_price = prices[0]
    
    # === 策略参数 (针对空头优化) ===
    initial_stop_loss = 0.02    # 初始止损+2%
    stage1_threshold = 0.008    # 0.8%盈利进入阶段1
    stage2_threshold = 0.018    # 1.8%盈利进入阶段2
    stage3_threshold = 0.025    # 2.5%盈利进入阶段3
    stage1_stop_loss = 0.012    # 阶段1止损+1.2%
    stage2_stop_profit = 0.005  # 阶段2止损设在-0.5%
    trailing_ratio = 0.35       # 跟踪止损35%回撤
    
    # === 交易状态初始化 ===
    position = True
    stage = 0
    stop_loss = entry_price * (1 + initial_stop_loss)
    lowest_price = entry_price
    
    # 记录历史数据用于绘图
    stage_history = [0]
    stop_loss_history = [stop_loss]
    profit_history = [0.0]
    
    if show_details:
        print("=== 空头多重确认阶段性策略 ===")
        print(f"开仓价格: {entry_price:.5f}")
        print(f"初始止损: {stop_loss:.5f} (+{initial_stop_loss*100:.1f}%)")
        # 正确的计算
        print(f"理论收益: {((entry_price - prices[-1]) / entry_price * 100):.2f}%")
        print("-" * 50)
    
    # === 逐个处理价格 ===
    for i, current_price in enumerate(prices[1:], 1):
        if not position:
            break
        
        # 更新最低价
        lowest_price = min(lowest_price, current_price)
        
        # 计算当前盈利
        current_profit = (entry_price - current_price) / entry_price
        
        # === 阶段升级逻辑 ===
        old_stage = stage
        
        if stage == 0 and current_profit >= stage1_threshold:
            stage = 1
            new_stop = entry_price * (1 + stage1_stop_loss)
            stop_loss = min(stop_loss, new_stop)
            
        elif stage == 1 and current_profit >= stage2_threshold:
            stage = 2
            new_stop = entry_price * (1 - stage2_stop_profit)
            stop_loss = min(stop_loss, new_stop)
            
        elif stage == 2 and current_profit >= stage3_threshold:
            stage = 3
        
        # === 阶段3跟踪止损 ===
        if stage == 3:
            profit_range = entry_price - lowest_price
            trailing_distance = profit_range * trailing_ratio
            new_stop = lowest_price + trailing_distance
            stop_loss = min(stop_loss, new_stop)
        
        # 记录历史
        stage_history.append(stage)
        stop_loss_history.append(stop_loss)
        profit_history.append(current_profit * 100)
        
        # 显示阶段升级
        if show_details and old_stage != stage:
            print(f"第{i:2d}天: 阶段{old_stage}→{stage} | 价格:{current_price:.5f} | 盈利:{current_profit*100:.2f}% | 止损:{stop_loss:.5f}")
        
        # === 检查止损 ===
        if current_price >= stop_loss:
            final_profit = (entry_price - current_price) / entry_price * 100
            max_profit = (entry_price - lowest_price) / entry_price * 100
            
            if show_details:
                print(f"\n第{i:2d}天: 🛑 触发止损!")
                print(f"当前价格: {current_price:.5f}")
                print(f"最终收益: {final_profit:.2f}%")
                print(f"最大浮盈: {max_profit:.2f}%")
                print(f"最终阶段: {stage}")
            
            result = {
                'success': False,
                'exit_day': i,
                'exit_price': current_price,
                'final_profit': final_profit,
                'max_profit': max_profit,
                'final_stage': stage,
                'reason': 'STOP_LOSS',
                'entry_price': entry_price,
                'prices': prices,
                'stage_history': stage_history,
                'stop_loss_history': stop_loss_history,
                'profit_history': profit_history,
                'lowest_price': lowest_price
            }
            
            if show_chart:
                plot_strategy_chart(result, save_path)
            
            return result
        
        # 显示关键节点
        if show_details and i % 15 == 0:
            print(f"第{i:2d}天: 价格:{current_price:.5f} | 阶段:{stage} | 止损:{stop_loss:.5f} | 浮盈:{current_profit*100:.2f}%")
    
    # === 成功持有到最后 ===
    final_price = prices[-1]
    final_profit = (entry_price - final_price) / entry_price * 100
    max_profit = (entry_price - lowest_price) / entry_price * 100
    
    if show_details:
        print(f"\n🎉 空头策略成功! 完整捕获趋势!")
        print(f"最终价格: {final_price:.5f}")
        print(f"最终收益: {final_profit:.2f}%")
        print(f"最大浮盈: {max_profit:.2f}%")
        print(f"最终阶段: {stage}")
        print(f"持有天数: {len(prices)-1}")
    
    result = {
        'success': True,
        'exit_day': len(prices) - 1,
        'exit_price': final_price,
        'final_profit': final_profit,
        'max_profit': max_profit,
        'final_stage': stage,
        'reason': 'MANUAL_EXIT',
        'entry_price': entry_price,
        'prices': prices,
        'stage_history': stage_history,
        'stop_loss_history': stop_loss_history,
        'profit_history': profit_history,
        'lowest_price': lowest_price
    }
    
    if show_chart:
        plot_strategy_chart(result, save_path)
    
    return result

def plot_strategy_chart(result_data, save_path=None):
    """
    绘制策略执行图表
    
    Args:
        result_data: 包含历史数据的结果字典
        save_path: 保存路径，None则显示图表
    """
    try:
        prices = result_data['prices']
        stop_loss_history = result_data['stop_loss_history']
        stage_history = result_data['stage_history']
        profit_history = result_data['profit_history']
        entry_price = result_data['entry_price']
        
        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans', 'Arial Unicode MS']
        plt.rcParams['axes.unicode_minus'] = False
        
        # 创建图表
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(15, 10))
        
        # === 上图：价格走势和止损线 ===
        x = range(len(prices))
        
        # 绘制价格线
        ax1.plot(x, prices, 'b-', linewidth=2.5, label='价格走势', alpha=0.8)
        
        # 绘制止损线
        if len(stop_loss_history) > 1:
            stop_x = range(1, len(stop_loss_history))
            ax1.plot(stop_x, stop_loss_history[1:], 'r--', linewidth=2, 
                    label='动态止损线', alpha=0.8)
        
        # 开仓价格线
        ax1.axhline(y=entry_price, color='green', linestyle=':', 
                   linewidth=2, label='开仓价格', alpha=0.7)
        
        # 添加阶段背景色
        stage_colors = ['#ffcccc', '#ffd9b3', '#ccffcc', '#cce6ff']
        stage_names = ['阶段0(风险期)', '阶段1(确认期)', '阶段2(安全期)', '阶段3(跟踪期)']
        
        if stage_history:
            for stage_num in range(4):
                if stage_num in stage_history:
                    stage_start = None
                    for i, s in enumerate(stage_history):
                        if s == stage_num and stage_start is None:
                            stage_start = i
                        elif s != stage_num and stage_start is not None:
                            ax1.axvspan(stage_start, i-1, alpha=0.2, 
                                       color=stage_colors[stage_num],
                                       label=stage_names[stage_num])
                            stage_start = None
                    # 处理最后一个阶段
                    if stage_start is not None:
                        ax1.axvspan(stage_start, len(stage_history)-1, alpha=0.2, 
                                   color=stage_colors[stage_num],
                                   label=stage_names[stage_num])
        
        # 标注关键点
        max_price = max(prices)
        min_price = min(prices)
        max_idx = prices.index(max_price)
        min_idx = prices.index(min_price)
        
        ax1.scatter([max_idx], [max_price], color='red', s=100, zorder=5)
        ax1.annotate(f'最高点\n{max_price:.5f}\n(+{((max_price-entry_price)/entry_price*100):.2f}%)', 
                    xy=(max_idx, max_price), xytext=(10, 10), 
                    textcoords='offset points', fontsize=9,
                    bbox=dict(boxstyle='round,pad=0.3', facecolor='red', alpha=0.7))
        
        ax1.scatter([min_idx], [min_price], color='blue', s=100, zorder=5)
        ax1.annotate(f'最低点\n{min_price:.5f}\n({((entry_price-min_price)/entry_price*100):.2f}%)', 
                    xy=(min_idx, min_price), xytext=(10, -20), 
                    textcoords='offset points', fontsize=9,
                    bbox=dict(boxstyle='round,pad=0.3', facecolor='blue', alpha=0.7))
        
        ax1.set_title('空头多重确认阶段性止损策略执行图表', fontsize=16, fontweight='bold', pad=20)
        ax1.set_xlabel('时间 (天)', fontsize=12)
        ax1.set_ylabel('价格', fontsize=12)
        ax1.legend(loc='best', fontsize=10)
        ax1.grid(True, alpha=0.3)
        
        # === 下图：盈亏变化曲线 ===
        if profit_history:
            profit_x = range(len(profit_history))
            ax2.plot(profit_x, profit_history, 'purple', linewidth=2.5, 
                    label='浮动盈亏 (%)', alpha=0.8)
            ax2.axhline(y=0, color='black', linestyle='-', alpha=0.5, linewidth=1)
            
            # 填充盈利/亏损区域
            positive_mask = np.array(profit_history) >= 0
            negative_mask = np.array(profit_history) < 0
            
            ax2.fill_between(profit_x, profit_history, 0, 
                            where=positive_mask, alpha=0.3, color='green', label='盈利区域')
            ax2.fill_between(profit_x, profit_history, 0, 
                            where=negative_mask, alpha=0.3, color='red', label='亏损区域')
            
            # 标注最终收益
            final_profit = profit_history[-1]
            ax2.scatter([len(profit_history)-1], [final_profit], 
                       color='purple', s=100, zorder=5)
            ax2.annotate(f'最终收益\n{final_profit:.2f}%', 
                        xy=(len(profit_history)-1, final_profit), 
                        xytext=(10, 10), textcoords='offset points', fontsize=10,
                        bbox=dict(boxstyle='round,pad=0.3', facecolor='purple', alpha=0.7))
        
        ax2.set_title('盈亏变化曲线', fontsize=14, fontweight='bold')
        ax2.set_xlabel('时间 (天)', fontsize=12)
        ax2.set_ylabel('盈亏 (%)', fontsize=12)
        ax2.legend(loc='best', fontsize=10)
        ax2.grid(True, alpha=0.3)
        
        # 策略信息文本框
        info_text = f"""策略信息:
开仓价格: {entry_price:.5f}
最终价格: {result_data['exit_price']:.5f}
最终收益: {result_data['final_profit']:.2f}%
最大浮盈: {result_data['max_profit']:.2f}%
最终阶段: {result_data['final_stage']}
交易结果: {'成功' if result_data['success'] else '止损'}"""
        
        ax1.text(0.02, 0.98, info_text, transform=ax1.transAxes, fontsize=10,
                verticalalignment='top', bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight', facecolor='white')
            print(f"图表已保存到: {save_path}")
        else:
            plt.show()
            
    except Exception as e:
        print(f"绘图出现错误: {e}")
        print("可能需要安装matplotlib: pip install matplotlib")

def compare_stop_strategies(prices, entry_price=None):
    """
    对比不同止损策略的效果
    """
    if entry_price is None:
        entry_price = prices[0]
    
    print("=== 空头策略效果对比 ===")
    print(f"开仓价: {entry_price:.5f}")
    print(f"最终价: {prices[-1]:.5f}")
    print(f"最大反弹: +{((max(prices) - entry_price) / entry_price * 100):.2f}%")
    print("-" * 40)
    
    # 测试固定止损
    fixed_stops = [0.01, 0.012, 0.015, 0.02, 0.025]
    
    for stop_pct in fixed_stops:
        stop_price = entry_price * (1 + stop_pct)
        hit_stop = False
        exit_day = 0
        
        for i, price in enumerate(prices[1:], 1):
            if price >= stop_price:
                hit_stop = True
                exit_day = i
                break
        
        if hit_stop:
            loss = (entry_price - prices[exit_day]) / entry_price * 100
            print(f"{stop_pct*100:4.1f}%固定止损: ❌ 第{exit_day}天止损 | 亏损{abs(loss):.2f}%")
        else:
            profit = (entry_price - prices[-1]) / entry_price * 100
            print(f"{stop_pct*100:4.1f}%固定止损: ✅ 成功持有 | 收益{profit:.2f}%")
    
    # 测试阶段性策略
    result = short_dynamic_stop_strategy(prices, entry_price, show_details=False)
    status = "✅ 成功" if result['success'] else "❌ 止损"
    print(f"阶段性策略:     {status} | 收益{result['final_profit']:.2f}%")

def testSell( list):
    buysellHistory=list
    resultBuy=0
    for i in range(0,len(buysellHistory[0])):
        if buysellHistory[0][i]== -1:
            test_prices=buysellHistory[1][i]
            # print(test_prices)
            
            # print("\n=== 空头动态止损策略验证 ===")
            # print(f"价格数据点数: {len(test_prices)}")
            # print(f"开仓价格: {test_prices[0]:.5f}")
            # print(f"最终价格: {test_prices[-1]:.5f}")
            # print(f"理论最大收益: {((test_prices[0]-test_prices[-1] ) / test_prices[0] * 100):.2f}%")
            
            # 单策略回测
            # print("=== 单策略回测 ===")
          # 1. 执行阶段性策略
            result = short_dynamic_stop_strategy(test_prices)
                # # 2. 对比各种策略
            # compare_stop_strategies(test_prices)
            resultBuy+=result['final_profit']
    print(resultBuy)
    print(f"最终收益: {resultBuy:.2f}%")
    
def main():
    """
    主函数 - 使用你的实际数据测试
    """
    # 你的空头测试数据
    test_prices = [
        0.38382, 0.38555, 0.3849, 0.38525, 0.38512, 0.38646, 0.38616, 0.386, 
        0.38576, 0.38604, 0.38596, 0.38612, 0.38524, 0.38528, 0.38484, 0.38563, 
        0.38577, 0.38573, 0.38575, 0.3856, 0.38597, 0.38632, 0.38615, 0.38597, 
        0.38675, 0.38698, 0.3865, 0.38609, 0.38673, 0.38678, 0.38687, 0.38719, 
        0.3879, 0.38781, 0.3878, 0.38676, 0.38639, 0.38666, 0.38699, 0.38697, 
        0.38667, 0.38622, 0.386, 0.38646, 0.38697, 0.38766, 0.38757, 0.38717, 
        0.38606, 0.38606, 0.3859, 0.38666, 0.38657, 0.38731, 0.38744, 0.38689, 
        0.3852, 0.38621, 0.38642, 0.38671, 0.38628, 0.38569, 0.38444, 0.38305, 
        0.38075, 0.38088, 0.37809, 0.37398, 0.3729
    ]
    
    buysellHistory=load_json('buysellHistory.json')
    # print(buysellHistory[0][0])
    # print(buysellHistory[1][0])
    # print(buysellHistory[2][0])
    print(len(buysellHistory[0]))
    
    resultBuy=0
    menoy=20
    menoybucong=0
    count=0
    # for i in range(0,len(buysellHistory[0])):
    #     # max=[]
    #     if buysellHistory[0][i]== -1:
            
    #         lenPrice=len(buysellHistory[1][i])
    #         enterprice=buysellHistory[1][i][0]
    #         maxPrice = max(buysellHistory[1][i]) # 使用内置 max() 函数
    #         minPrice = min(buysellHistory[2][i]) # 使用内置 min() 函数
    #         print(f"当前数据的长度 (lenPrice): {lenPrice}")
    #         print(f"开盘价 (enterprice): {enterprice}")
    #         print(f"最大值 (maxPrice): {maxPrice}")
    #         print(f"最小值 (minPrice): {minPrice}")
          
    #         # print(f"high:{buysellHistory[1][i]}")
    #         # print(f"low:{buysellHistory[2][i]}",'\n')
            
    #         # print("\n=== 空头动态止损策略验证 ===")
    #         # print(f"价格数据点数: {len(test_prices)}")
    #         # print(f"开仓价格: {test_prices[0]:.5f}")
    #         # print(f"最终价格: {test_prices[-1]:.5f}")
    #         # print(f"理论最大收益: {((test_prices[0]-test_prices[-1] ) / test_prices[0] * 100):.2f}%")


    
    
    for i in range(0,len(buysellHistory[0])):
        if buysellHistory[0][i]== -1:
            test_prices=buysellHistory[2][i]
            jixian_price=buysellHistory[1][i]
            # print(test_prices)
            
            print("\n=== 空头动态止损策略验证 ===")
            print(f"价格数据点数: {len(test_prices)}")
            print(f"开仓价格: {test_prices[0]:.5f}")
            print(f"最终价格: {test_prices[-1]:.5f}")
            print(f"理论最大收益: {((test_prices[0]-test_prices[-1] ) / test_prices[0] * 100):.2f}%")
            
            # 单策略回测
            print("=== 单策略回测 ===")
          # 1. 执行阶段性策略
            result = short_dynamic_stop_strategy(test_prices,jixian_price)
                # # 2. 对比各种策略
            # compare_stop_strategies(test_prices)
            # if menoy<10:
            #     menoy+=10
            #     menoybucong+=10
            menoy=menoy*(1+result['final_profit']/10)
            count+=1
            resultBuy+=result['final_profit']
            print(count,menoy)
            # if menoy>=1000:
            #     break
    # print(resultBuy)
    print(f"最终收益: {resultBuy:.2f}%")
    
    
    # print(menoy,menoybucong)
    
    
    
    # print("=== 空头动态止损策略验证 ===\n")
    
    # # 1. 执行阶段性策略
    # result = short_dynamic_stop_strategy(test_prices, entry_price=0.38382)
    
    # print("\n" + "="*50)
    
    # # 2. 对比各种策略
    # compare_stop_strategies(test_prices, entry_price=0.38382)
    
    # print("\n=== 策略优势总结 ===")
    # print("1. 🎯 智能容错: 2%初始止损抗住1.06%最大反弹")
    # print("2. 📈 渐进收紧: 随盈利增加自动收紧止损保护利润") 
    # print("3. 🔒 利润锁定: 1.8%盈利后止损移至盈利区")
    # print("4. 🏃 趋势跟踪: 2.5%盈利后动态跟踪最大化收益")
    # print("5. ✅ 实战验证: 完整捕获2.85%理论收益")
    
    # # 3. 显示图表
    # print("\n=== 策略执行图表展示 ===")
    # chart_result = short_dynamic_stop_strategy_with_chart(test_prices, entry_price=0.38382, 
    #                                                      show_details=False, show_chart=True)
    
    # return result

if __name__ == "__main__":
    # 运行验证
    main()
    
    # 快速测试你的数据:
    # your_prices = [你的价格序列]
    # result = short_dynamic_stop_strategy(your_prices)  # 不带图表
    # result = short_dynamic_stop_strategy_with_chart(your_prices)  # 带图表
    # print(f"策略收益: {result['final_profit']:.2f}%")